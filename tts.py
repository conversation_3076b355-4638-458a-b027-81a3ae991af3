from gtts import gTTS
import os
import random
import time
import uuid

# List of available TTS providers and voices
TTS_PROVIDERS = {
    "google": {
        "function": "generate_google_tts",
        "voices": ["en-US", "en-GB", "en-AU", "en-IN", "en-CA"]
    },
    "edge": {
        "function": "generate_edge_tts",
        "voices": [
            "en-US-AriaNeural", "en-US-GuyNeural", "en-US-JennyNeural",
            "en-GB-SoniaNeural", "en-GB-RyanNeural", "en-AU-NatashaNeural",
            "en-AU-WilliamNeural", "en-CA-ClaraNeural", "en-CA-LiamNeural"
        ]
    }
}

def generate_tts(text, voice=None, provider=None, output_path="output/voice.mp3"):
    """
    Generate text-to-speech audio with random or specified voice and provider

    Args:
        text (str): Text to convert to speech
        voice (str, optional): Specific voice to use
        provider (str, optional): TTS provider to use ('google' or 'edge')
        output_path (str, optional): Path to save the audio file

    Returns:
        str: Path to the generated audio file
    """
    # If no provider specified, choose randomly
    if not provider:
        provider = random.choice(list(TTS_PROVIDERS.keys()))

    # If provider not valid, default to google
    if provider not in TTS_PROVIDERS:
        provider = "google"

    # If no voice specified, choose randomly from the provider's voices
    if not voice:
        voice = random.choice(TTS_PROVIDERS[provider]["voices"])

    # Generate unique filename based on timestamp and random ID
    timestamp = int(time.time())
    random_id = str(uuid.uuid4())[:8]
    filename = f"voice_{timestamp}_{random_id}.mp3"
    output_path = os.path.join("output", filename)

    # Call the appropriate TTS function based on provider
    if provider == "google":
        return generate_google_tts(text, voice, output_path)
    elif provider == "edge":
        return generate_edge_tts(text, voice, output_path)
    else:
        # Fallback to Google TTS
        return generate_google_tts(text, "en-US", output_path)

def generate_google_tts(text, lang="en-US", output_path="output/voice.mp3"):
    """Generate TTS using Google's gTTS"""
    try:
        # If lang is not a valid language code, default to en-US
        if lang not in ["en-US", "en-GB", "en-AU", "en-IN", "en-CA"]:
            lang = "en-US"

        # Convert language code to gTTS format (just 'en' for English)
        gtts_lang = lang.split('-')[0]

        # Create gTTS object
        tts = gTTS(text=text, lang=gtts_lang, tld=lang.split('-')[1].lower())
        tts.save(output_path)
        print(f"Generated Google TTS with voice: {lang}")
        return output_path
    except Exception as e:
        print(f"Error generating Google TTS: {e}")
        # Fallback to basic gTTS
        tts = gTTS(text=text, lang='en')
        tts.save(output_path)
        return output_path

def generate_edge_tts(text, voice="en-US-AriaNeural", output_path="output/voice.mp3"):
    """Generate TTS using Microsoft Edge TTS (via subprocess)"""
    try:
        # Check if edge-tts is installed
        import importlib.util
        edge_tts_installed = importlib.util.find_spec("edge_tts") is not None

        if not edge_tts_installed:
            print("Edge TTS not installed. Installing...")
            os.system("pip install edge-tts")

        # Import edge_tts after ensuring it's installed
        import edge_tts
        import asyncio

        # Define async function to generate TTS
        async def _generate_edge_tts():
            communicate = edge_tts.Communicate(text, voice)
            await communicate.save(output_path)

        # Run the async function
        asyncio.run(_generate_edge_tts())

        print(f"Generated Edge TTS with voice: {voice}")
        return output_path
    except Exception as e:
        print(f"Error generating Edge TTS: {e}, falling back to Google TTS")
        # Fallback to Google TTS
        return generate_google_tts(text, "en-US", output_path)
