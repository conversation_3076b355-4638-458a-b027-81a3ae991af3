import os
import random
import requests
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def generate_script():
    """
    Generate a script for a faceless video.

    Returns:
        str: A 3-line script ready for video production
    """
    # Get API key from environment variables
    api_key = os.getenv("OPENROUTER_API_KEY")

    if not api_key:
        print("Warning: No OPENROUTER_API_KEY found in environment variables.")
        return

    # Define script types and their corresponding hooks and topics
    script_types = {
        "fact": {
            "hooks": ["Did you know", "Here's a mind-blowing fact", "This fact will surprise you", "Wait till you hear this"],
            "topics": ["space", "animals", "human body", "ocean", "world records", "weird laws", "food science"]
        },
        "psychology": {
            "hooks": ["This psychology trick works every time", "Your brain is playing tricks on you", "Try this mind hack"],
            "topics": ["cognitive biases", "social psychology", "persuasion techniques", "memory tricks", "body language"]
        },
        "history": {
            "hooks": ["History doesn't tell you this", "This historical fact is hidden", "Ancient people knew this secret"],
            "topics": ["ancient civilizations", "historical figures", "wars", "inventions", "lost technologies"]
        },
        "science": {
            "hooks": ["Scientists just discovered", "This scientific fact will blow your mind", "Science explains why"],
            "topics": ["quantum physics", "biology", "chemistry", "astronomy", "neuroscience", "evolution"]
        },
        "tech": {
            "hooks": ["This tech trick saves time", "Your phone has a hidden feature", "AI can now do this"],
            "topics": ["smartphone hacks", "AI advancements", "future tech", "digital privacy", "coding tricks"]
        },
        "life_hack": {
            "hooks": ["This life hack will save you hours", "Try this simple trick", "You've been doing this wrong"],
            "topics": ["productivity", "home organization", "money saving", "cooking hacks", "fitness shortcuts"]
        },
        "motivation": {
            "hooks": ["This changed my life", "The secret to success is", "Successful people always do this"],
            "topics": ["habits", "mindset", "goal setting", "overcoming failure", "building discipline"]
        }
    }

    script_type = random.choice(list(script_types.keys()))

    # Get hooks and topics for the selected type
    hooks = script_types[script_type]["hooks"]
    topics = script_types[script_type]["topics"]

    # Randomly select a hook and topic
    hook = random.choice(hooks)
    topic = random.choice(topics)

    # Create a detailed prompt for better script generation
    prompt = f"""
    Create a short, engaging script for a faceless YouTube Shorts/TikTok video about {topic}.

    Start with the hook: "{hook}"

    Requirements:
    - Exactly 3 lines of text (separated by line breaks)
    - Each line should be concise (max 15 words per line)
    - Include a surprising fact or insight
    - End with something thought-provoking or a call to action
    - Make it conversational, as if talking directly to the viewer
    - DO NOT include any hashtags, emojis, or formatting
    - DO NOT include any introduction or conclusion text

    Return ONLY the 3 lines of the script, nothing else.
    """

    try:
        # Make API request to OpenRouter
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json={
                "model": "openai/gpt-3.5-turbo",  # You can change this to other models
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,  # Add some creativity but not too random
                "max_tokens": 150    # Limit response length
            }
        )

        # Check if request was successful
        if response.status_code == 200:
            result = response.json()
            script = result["choices"][0]["message"]["content"].strip()

            # Clean up the script
            # Remove any extra text before or after the actual script
            lines = [line.strip() for line in script.split('\n') if line.strip()]

            # Take only the first 3 non-empty lines
            clean_lines = []
            for line in lines:
                # Skip lines that look like headers, numbering, or instructions
                if line.startswith(('1.', '2.', '3.', '#', 'Line', 'Script:')):
                    continue
                # Skip very short lines that might be separators
                if len(line) < 5:
                    continue
                clean_lines.append(line)
                if len(clean_lines) >= 3:
                    break

            # If we don't have exactly 3 lines, use fallback
            if len(clean_lines) != 3:
                print("API didn't return a proper 3-line script, using fallback")

            # Join the lines with newlines
            final_script = '\n'.join(clean_lines)
            return final_script
        else:
            print(f"Error from OpenRouter API: {response.status_code}")
            print(f"Response: {response.text}")

    except Exception as e:
        print(f"Exception when calling OpenRouter API: {e}")
