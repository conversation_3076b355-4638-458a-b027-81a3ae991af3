import os
import random
import textwrap
from moviepy.video.io.VideoFileClip import VideoFile<PERSON>lip
from moviepy.audio.io.AudioFileClip import AudioFileClip
from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip
from moviepy.video.VideoClip import TextClip, ColorClip

def generate_video(script_text, audio_path, output_path="output/final_output.mp4"):
    """Generate a video with gameplay footage, audio, and captions"""
    # Get a random gameplay video
    gameplay_folder = "assets/gameplay"
    files = os.listdir(gameplay_folder)
    video_path = os.path.join(gameplay_folder, random.choice(files))

    # Load the video and audio
    video = VideoFileClip(video_path)
    audio = AudioFileClip(audio_path)

    # Get video dimensions
    video_width, video_height = video.size

    # Resize video if needed (optional)
    # video = video.resize(width=1080)  # For vertical format like shorts

    # Simply use the beginning portion of the video to match audio duration
    # This is simpler than trying to loop the video
    video_duration = video.duration
    audio_duration = audio.duration

    # If video is longer than audio, just use the beginning portion
    if video_duration > audio_duration:
        video = video.subclipped(0, audio_duration)
    # If video is shorter, we'll just let it play and the audio might continue after video ends
    # This is a simpler approach than trying to loop the video

    # Add audio to video
    video = video.with_audio(audio)

    # Split script into lines
    lines = script_text.split('\n')

    # Calculate duration per line
    duration = audio.duration
    per_line = duration / len(lines)

    # Create clips list starting with the video
    clips = [video]

    # Maximum width for text (as a percentage of video width)
    max_text_width = int(video_width * 0.85)  # 85% of video width

    # Calculate appropriate font size based on video dimensions
    base_font_size = int(video_height / 15)  # Adjust this ratio as needed

    # Padding at the bottom of the screen (in pixels)
    bottom_padding = int(video_height * 0.05)  # 5% of video height

    for i, line in enumerate(lines):
        # Wrap text if it's too long
        if len(line) > 30:  # Adjust this threshold as needed
            wrapped_text = textwrap.fill(line, width=30)
        else:
            wrapped_text = line

        # Create text clip with improved visibility
        txt = TextClip(
            text=wrapped_text,
            font="/usr/share/fonts/noto/NotoSans-Bold.ttf",
            font_size=base_font_size,
            color='white',
            stroke_color='black',
            stroke_width=2,
            method='caption',
            size=(max_text_width, None),
            text_align='center'
        )

        # Create a semi-transparent background for better readability
        txt_bg = ColorClip(
            size=(txt.w + 40, txt.h + 20),  # Slightly larger than text
            color=(0, 0, 0, 0.6)  # Semi-transparent black
        ).with_duration(per_line).with_start(i * per_line)

        # Position the background
        txt_bg = txt_bg.with_position(
            ("center", video_height - txt.h - bottom_padding - 10)
        )

        # Position the text
        txt = txt.with_position(
            ("center", video_height - txt.h - bottom_padding)
        ).with_duration(per_line).with_start(i * per_line)

        # Add background and text to clips
        clips.append(txt_bg)
        clips.append(txt)

    # Combine all clips
    final = CompositeVideoClip(clips)

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Write the final video
    final.write_videofile(output_path, fps=30)
