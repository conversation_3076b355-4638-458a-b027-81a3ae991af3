import os
from pathlib import Path
from openai import OpenAI

# Check if API key is set
api_key = os.getenv('OPENAI_API_KEY')
if not api_key:
    print("Error: OPENAI_API_KEY environment variable is not set.")
    print("Please set your OpenAI API key:")
    print("export OPENAI_API_KEY='your-api-key-here'")
    exit(1)

client = OpenAI(api_key=api_key)
speech_file_path = Path(__file__).parent / "speech.mp3"

# Note: The TTS API doesn't support 'instructions' parameter
# Available models: tts-1, tts-1-hd
# Available voices: alloy, echo, fable, onyx, nova, shimmer
with client.audio.speech.with_streaming_response.create(
    model="tts-1",  # Changed from gpt-4o-mini-tts to tts-1
    voice="nova",   # Changed from coral to nova (coral is not available)
    input="Today is a wonderful day to build something people love!",
) as response:
    response.stream_to_file(speech_file_path)

print(f"Speech saved to: {speech_file_path}")