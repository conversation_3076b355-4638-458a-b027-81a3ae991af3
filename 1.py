import requests, os, time, subprocess

BASE_URL = "https://client.camb.ai/apis"
API_KEY = "66f9ee7e-39ad-42c1-a795-1f78eac897f2"
HEADERS = {"headers": {"x-api-key": API_KEY}}

# Get available voices
voices_res = requests.get(f"{BASE_URL}/list-voices", **HEADERS)
if voices_res.status_code == 200:
    voices_data = voices_res.json()
    # Use the first available voice (<PERSON>)
    voice_id = voices_data[0]['id']
    voice_name = voices_data[0]['voice_name']
    print(f"Using voice: {voice_name}")
else:
    print("Could not get voices, using default")
    voice_id = 20298  # Daniel's ID from the previous response

tts_payload = {
    "text": "Hello world, my name is <PERSON><PERSON><PERSON>!",
    "voice_id": voice_id,
    "language": 1,  # English (required field)
}

res = requests.post(f"{BASE_URL}/tts", json=tts_payload, **HEADERS)
response_data = res.json()

if res.status_code != 200:
    error_msg = response_data.get('message', 'Unknown error')
    if 'detail' in response_data:
        error_msg = str(response_data['detail'])
    print(f"Error: {error_msg}")
    exit(1)

if "task_id" in response_data:
    task_id = response_data["task_id"]
elif "payload" in response_data and isinstance(response_data["payload"], dict) and "task_id" in response_data["payload"]:
    task_id = response_data["payload"]["task_id"]
else:
    print("Could not find task_id in response")
    exit(1)

print(f"Task ID: {task_id}")

while True:
    res = requests.get(f"{BASE_URL}/tts/{task_id}", **HEADERS)
    status = res.json()["status"]
    print(f"Polling: {status}")
    time.sleep(1.5)
    if status == "SUCCESS":
        run_id = res.json()["run_id"]
        break

print(f"Run ID: {run_id}")
res = requests.get(f"{BASE_URL}/tts-result/{run_id}", **HEADERS, stream=True)

# Save as temporary FLAC file
temp_flac = "temp_tts_output.flac"
with open(temp_flac, "wb") as f:
    for chunk in res.iter_content(chunk_size=1024):
        f.write(chunk)

print("Converting FLAC to MP3...")
# Convert FLAC to MP3 using ffmpeg
subprocess.run([
    "ffmpeg", "-i", temp_flac,
    "-codec:a", "libmp3lame",
    "-b:a", "192k",
    "tts_output.mp3",
    "-y"  # Overwrite output file if it exists
], check=True, capture_output=True)

# Clean up temporary FLAC file
os.remove(temp_flac)
print("Done! MP3 file created: tts_output.mp3")