import os
import random
import time
import uuid
from generate_script import generate_script
from tts import generate_tts, TTS_PROVIDERS
from video_editor import generate_video

def main():
    # Set default configuration - no inputs needed
    config = {
        'script_type': 'random',
        'voice_provider': 'random',
        'voice': None,
        'count': 1,
        'output_dir': 'output'
    }

    os.makedirs(config['output_dir'], exist_ok=True)

    # Generate the specified number of videos
    for i in range(config['count']):
        if config['count'] > 1:
            print(f"\n=== Generating Video {i+1}/{config['count']} ===\n")

        # Generate a unique output filename
        timestamp = int(time.time())
        random_id = str(uuid.uuid4())[:8]
        output_filename = f"video_{timestamp}_{random_id}.mp4"
        output_path = os.path.join(config['output_dir'], output_filename)

        # Convert 'random' to None for the script generator
        script_type = None if config['script_type'] == 'random' else config['script_type']

        # Generate script
        print(f"Generating {config['script_type']} script...")
        script = generate_script(script_type)
        print("\nScript:")
        print("-" * 40)
        print(script)
        print("-" * 40)

        # Set up voice provider
        if config['voice_provider'] == 'random':
            voice_provider = random.choice(list(TTS_PROVIDERS.keys()))
        else:
            voice_provider = config['voice_provider']

        # Generate voice
        print(f"\nGenerating voice using {voice_provider} provider...")
        audio_path = generate_tts(script, voice=config['voice'], provider=voice_provider)

        # Generate video
        print("\nStitching video...")
        generate_video(script, audio_path, output_path=output_path)

        # Clean up the temporary audio file
        if os.path.exists(audio_path):
            os.remove(audio_path)
            print(f"🗑️ Cleaned up audio file: {audio_path}")

        print(f"\n✅ Video generated: {output_path}")

    if config['count'] > 1:
        print(f"\n✅ Generated {config['count']} random videos in {config['output_dir']}/")

if __name__ == "__main__":
    main()
